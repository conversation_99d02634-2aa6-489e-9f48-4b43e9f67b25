/* Styles pour la version web d'AquaTrack */

/* Page de connexion */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.logo {
  text-align: center;
  margin-bottom: 30px;
}

.logo-icon {
  font-size: 60px;
  margin-bottom: 10px;
}

.logo h1 {
  color: #2196F3;
  font-size: 32px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.logo p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

.form-group input {
  width: 100%;
  border: 2px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  font-size: 16px;
  background: #fff;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #2196F3;
}

.login-btn {
  width: 100%;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 15px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-btn:hover {
  background: #1976d2;
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.test-info {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #2196F3;
}

.test-info h4 {
  color: #1976d2;
  margin: 0 0 5px 0;
  font-size: 14px;
}

.test-info p {
  color: #1976d2;
  margin: 0;
  font-size: 12px;
  font-family: monospace;
}

/* Dashboard */
.dashboard {
  min-height: 100vh;
  background: #f8fafc;
}

.dashboard-header {
  background: #3b82f6;
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0;
  font-size: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logout-btn {
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 600;
}

.logout-btn:hover {
  background: #b91c1c;
}

.dashboard-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.welcome-card h2 {
  color: #1f2937;
  margin: 0 0 10px 0;
  font-size: 28px;
}

.welcome-card p {
  color: #6b7280;
  margin: 0;
  font-size: 18px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.dashboard-card h3 {
  color: #1f2937;
  margin: 0 0 10px 0;
  font-size: 20px;
}

.dashboard-card p {
  color: #6b7280;
  margin: 0;
  font-size: 16px;
}

.info-message {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 20px;
  margin-top: 30px;
}

.info-message h3 {
  color: #92400e;
  margin: 0 0 15px 0;
}

.info-message p {
  color: #92400e;
  margin: 0 0 10px 0;
  line-height: 1.6;
}

.info-message ul {
  color: #92400e;
  margin: 10px 0 0 20px;
}

.info-message code {
  background: #fbbf24;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}
