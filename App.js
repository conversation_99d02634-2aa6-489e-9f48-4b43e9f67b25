import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import des pages principales
import TechnicianDashboard from './src/pages/TechnicianDashboard';
import HistoriquePageRN from './HistoriquePageRN';
import ConsommationPage from './src/pages/ConsommationPage';
import FacturePage from './src/pages/FacturePage';
import QRCodePage from './src/pages/QRCodePage';

const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();

// Configuration de l'API - Utilisation de l'adresse IP de votre ordinateur
const API_BASE_URL = 'http://***********:4000';

// Écran de connexion React Native
const LoginScreen = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Vérifier si l'utilisateur est déjà connecté au démarrage
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const user = JSON.parse(userData);
        console.log('👤 Utilisateur déjà connecté:', user.nom, user.prenom);
        navigation.replace('Main', { user });
      }
    } catch (error) {
      console.log('❌ Erreur lors de la vérification du statut d\'authentification:', error);
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        // Sauvegarder les données utilisateur localement
        await AsyncStorage.setItem('userData', JSON.stringify(data.user));
        await AsyncStorage.setItem('authToken', data.token);

        Alert.alert(
          'Connexion réussie',
          `Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Vérifier le rôle et rediriger
                if (data.user.role === 'Tech' || data.user.role === 'Admin') {
                  console.log(`👨‍🔧 Accès autorisé - ${data.user.role}`);
                  navigation.replace('Main', { user: data.user });
                } else {
                  Alert.alert('Erreur', 'Rôle utilisateur non autorisé');
                }
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur de connexion', data.message || 'Erreur de connexion');
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion',
        'Impossible de se connecter au serveur. Vérifiez votre connexion internet et que le serveur est démarré.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          {/* Logo et titre */}
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Ionicons name="water" size={60} color="#2196F3" />
            </View>
            <Text style={styles.logoTitle}>AquaTrack</Text>
            <Text style={styles.logoSubtitle}>Système de Facturation Mobile</Text>
          </View>

          {/* Formulaire de connexion */}
          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <Ionicons name="mail-outline" size={20} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
            </View>

            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={20} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Mot de passe"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? "eye-outline" : "eye-off-outline"}
                  size={20}
                  color="#666"
                />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[styles.loginButton, loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.loginButtonText}>Se connecter</Text>
              )}
            </TouchableOpacity>

            {/* Informations de test */}
            <View style={styles.testInfoContainer}>
              <Text style={styles.testInfoTitle}>Compte de test :</Text>
              <Text style={styles.testInfoText}>Email: <EMAIL></Text>
              <Text style={styles.testInfoText}>Mot de passe: Tech123</Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// Composant pour le contenu personnalisé du drawer
const CustomDrawerContent = ({ navigation, route }) => {
  const user = route.params?.user;

  const menuItems = [
    {
      title: '🏠 Tableau de Bord',
      screen: 'Dashboard',
      icon: '🏠'
    },
    {
      title: '👥 Liste des Clients',
      screen: 'ClientsList',
      icon: '👥'
    },
    {
      title: '💧 Consommation',
      screen: 'Consommation',
      icon: '💧'
    },
    {
      title: '🧾 Factures',
      screen: 'Factures',
      icon: '🧾'
    },
    {
      title: '📱 Scanner QR',
      screen: 'QRCode',
      icon: '📱'
    },
  ];

  const handleLogout = async () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Déconnexion',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('userData');
              await AsyncStorage.removeItem('authToken');
              console.log('🚪 Déconnexion réussie');
              navigation.replace('Login');
            } catch (error) {
              console.log('❌ Erreur lors de la déconnexion:', error);
            }
          },
        },
      ]
    );
  };

  return (
    <View style={styles.drawerContainer}>
      <View style={styles.drawerHeader}>
        <Text style={styles.drawerTitle}>💧 AquaTrack</Text>
        <Text style={styles.drawerSubtitle}>Gestion de Facturation</Text>
        {user && (
          <Text style={styles.userInfo}>
            Bonjour {user.prenom} {user.nom}
          </Text>
        )}
      </View>

      <View style={styles.menuContainer}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={() => navigation.navigate(item.screen)}
          >
            <Text style={styles.menuIcon}>{item.icon}</Text>
            <Text style={styles.menuText}>{item.title}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.drawerFooter}>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Text style={styles.logoutText}>🚪 Déconnexion</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Navigation avec Drawer pour les pages principales
const DrawerNavigator = ({ route }) => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} route={route} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: '#f8fafc',
          width: 280,
        },
      }}
    >
      <Drawer.Screen
        name="Dashboard"
        component={TechnicianDashboard}
        options={{ title: 'Tableau de Bord' }}
      />
      <Drawer.Screen
        name="ClientsList"
        component={HistoriquePageRN}
        options={{ title: 'Liste des Clients' }}
      />
      <Drawer.Screen
        name="Consommation"
        component={ConsommationPage}
        options={{ title: 'Consommation' }}
      />
      <Drawer.Screen
        name="Factures"
        component={FacturePage}
        options={{ title: 'Factures' }}
      />
      <Drawer.Screen
        name="QRCode"
        component={QRCodePage}
        options={{ title: 'Scanner QR' }}
      />
    </Drawer.Navigator>
  );
};

// Navigation principale avec Stack
const App = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{ title: 'Connexion' }}
        />
        <Stack.Screen
          name="Main"
          component={DrawerNavigator}
          options={{ title: 'Application' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  // Styles pour l'écran de connexion
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 30,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  logoTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
  },
  logoSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    marginBottom: 20,
    paddingHorizontal: 15,
    backgroundColor: '#f9f9f9',
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: '#333',
  },
  eyeIcon: {
    padding: 5,
  },
  loginButton: {
    backgroundColor: '#2196F3',
    borderRadius: 10,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  testInfoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  testInfoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 5,
  },
  testInfoText: {
    fontSize: 12,
    color: '#1976d2',
    fontFamily: 'monospace',
  },

  // Styles pour le drawer
  drawerContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  drawerHeader: {
    backgroundColor: '#3b82f6',
    padding: 20,
    paddingTop: 50,
  },
  drawerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  drawerSubtitle: {
    fontSize: 14,
    color: '#bfdbfe',
  },
  userInfo: {
    fontSize: 12,
    color: '#e0f2fe',
    marginTop: 8,
    fontStyle: 'italic',
  },
  menuContainer: {
    flex: 1,
    paddingTop: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  menuIcon: {
    fontSize: 20,
    marginRight: 15,
    width: 30,
  },
  menuText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  drawerFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  logoutButton: {
    backgroundColor: '#dc2626',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  logoutText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default App;
