import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  FlatList,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ActionHistoryPage = ({ navigation }) => {
  // Données simulées de l'historique des actions
  const [actions] = useState([
    {
      id: 1,
      type: 'Relevé Consommation',
      client: '<PERSON><PERSON> Jean',
      description: 'Relevé de consommation effectué - 25 m³',
      date: '2024-01-15',
      time: '14:30',
      status: 'Terminé',
      icon: 'water',
      color: '#10b981'
    },
    {
      id: 2,
      type: 'Scan QR Code',
      client: '<PERSON>',
      description: 'QR Code scanné avec succès',
      date: '2024-01-15',
      time: '13:45',
      status: 'Terminé',
      icon: 'qr-code',
      color: '#8b5cf6'
    },
    {
      id: 3,
      type: 'Consultation Facture',
      client: '<PERSON>',
      description: 'Facture FACT-2024-003 consultée',
      date: '2024-01-15',
      time: '12:20',
      status: 'Terminé',
      icon: 'document-text',
      color: '#f59e0b'
    },
    {
      id: 4,
      type: 'Localisation Client',
      client: 'Dupont <PERSON>',
      description: 'Client localisé sur Google Maps',
      date: '2024-01-15',
      time: '11:15',
      status: 'Terminé',
      icon: 'location',
      color: '#ef4444'
    },
    {
      id: 5,
      type: 'Connexion',
      client: '-',
      description: 'Connexion à l\'application',
      date: '2024-01-15',
      time: '09:00',
      status: 'Terminé',
      icon: 'log-in',
      color: '#3b82f6'
    },
  ]);

  const [searchText, setSearchText] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('Tous');

  const filters = ['Tous', 'Relevé Consommation', 'Scan QR Code', 'Consultation Facture', 'Localisation Client', 'Connexion'];

  const filteredActions = actions.filter(action => {
    const matchesSearch = action.client.toLowerCase().includes(searchText.toLowerCase()) ||
                         action.description.toLowerCase().includes(searchText.toLowerCase());
    const matchesFilter = selectedFilter === 'Tous' || action.type === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const getActionStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const todayActions = actions.filter(action => action.date === '2024-01-15'); // Simulé pour aujourd'hui
    
    return {
      total: actions.length,
      today: todayActions.length,
      releves: actions.filter(a => a.type === 'Relevé Consommation').length,
      scans: actions.filter(a => a.type === 'Scan QR Code').length
    };
  };

  const stats = getActionStats();

  const renderActionItem = ({ item }) => (
    <View style={styles.actionCard}>
      <View style={styles.actionHeader}>
        <View style={[styles.actionIcon, { backgroundColor: item.color + '20' }]}>
          <Ionicons name={item.icon} size={20} color={item.color} />
        </View>
        <View style={styles.actionInfo}>
          <Text style={styles.actionType}>{item.type}</Text>
          <Text style={styles.actionClient}>{item.client}</Text>
        </View>
        <View style={styles.actionTime}>
          <Text style={styles.timeText}>{item.time}</Text>
          <Text style={styles.dateText}>{item.date}</Text>
        </View>
      </View>
      
      <Text style={styles.actionDescription}>{item.description}</Text>
      
      <View style={styles.actionFooter}>
        <View style={[styles.statusBadge, { backgroundColor: '#10b981' + '20' }]}>
          <Ionicons name="checkmark-circle" size={14} color="#10b981" />
          <Text style={[styles.statusText, { color: '#10b981' }]}>{item.status}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#84cc16" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Historique Actions</Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.openDrawer()}
        >
          <Ionicons name="menu" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#6b7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher dans l'historique..."
            value={searchText}
            onChangeText={setSearchText}
            placeholderTextColor="#9ca3af"
          />
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{stats.total}</Text>
            <Text style={styles.statLabel}>Total Actions</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#84cc16' }]}>{stats.today}</Text>
            <Text style={styles.statLabel}>Aujourd'hui</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#10b981' }]}>{stats.releves}</Text>
            <Text style={styles.statLabel}>Relevés</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#8b5cf6' }]}>{stats.scans}</Text>
            <Text style={styles.statLabel}>Scans QR</Text>
          </View>
        </View>

        {/* Filter Buttons */}
        <View style={styles.filterContainer}>
          <Text style={styles.filterTitle}>Filtrer par type :</Text>
          <View style={styles.filterButtons}>
            {filters.slice(0, 3).map((filter) => (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.filterButton,
                  selectedFilter === filter && styles.filterButtonActive
                ]}
                onPress={() => setSelectedFilter(filter)}
              >
                <Text style={[
                  styles.filterButtonText,
                  selectedFilter === filter && styles.filterButtonTextActive
                ]}>
                  {filter}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Actions List */}
        <View style={styles.listContainer}>
          <Text style={styles.listTitle}>
            Mes Actions ({filteredActions.length})
          </Text>
          
          <FlatList
            data={filteredActions}
            renderItem={renderActionItem}
            keyExtractor={(item) => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#84cc16',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  menuButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: '#1f2937',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    flex: 1,
    marginHorizontal: 2,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#84cc16',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 10,
    color: '#6b7280',
    textAlign: 'center',
  },
  filterContainer: {
    marginBottom: 20,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  filterButtonActive: {
    backgroundColor: '#84cc16',
    borderColor: '#84cc16',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  listContainer: {
    flex: 1,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  listContent: {
    paddingBottom: 20,
  },
  actionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  actionInfo: {
    flex: 1,
  },
  actionType: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 2,
  },
  actionClient: {
    fontSize: 14,
    color: '#6b7280',
  },
  actionTime: {
    alignItems: 'flex-end',
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  dateText: {
    fontSize: 12,
    color: '#6b7280',
  },
  actionDescription: {
    fontSize: 14,
    color: '#4b5563',
    marginBottom: 12,
    lineHeight: 20,
  },
  actionFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default ActionHistoryPage;
