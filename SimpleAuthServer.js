const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: '*',
  credentials: true
}));
app.use(express.json());

// Utilisateurs de test en mémoire
const testUsers = [
  {
    id: 1,
    nom: 'Technicien',
    prenom: 'Test',
    email: '<EMAIL>',
    password: 'Tech123',
    role: 'Tech',
    adresse: 'Adresse Test',
    tel: '+216 74 987 654'
  },
  {
    id: 2,
    nom: 'Admin',
    prenom: 'Système',
    email: '<EMAIL>',
    password: 'Admin123',
    role: 'Admin',
    adresse: 'Adresse Admin',
    tel: '+216 71 123 456'
  }
];

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur d\'authentification AquaTrack (Mode Test)',
    status: 'Fonctionnel',
    port: PORT,
    mode: 'Test - Sans base de données'
  });
});

// 🔐 Route de connexion
app.post('/api/auth/login', async (req, res) => {
  console.log('📱 Requête de connexion reçue:', req.body);
  const { email, password } = req.body;

  try {
    // Validation des champs
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Recherche utilisateur dans les utilisateurs de test
    console.log('🔍 Recherche utilisateur:', email);
    const user = testUsers.find(u => u.email === email);

    if (!user) {
      console.log('❌ Utilisateur non trouvé');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    console.log('👤 Utilisateur trouvé:', user.nom, user.prenom, '- Rôle:', user.role);

    // Vérification du mot de passe
    if (password !== user.password) {
      console.log('❌ Mot de passe incorrect');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // Déterminer la redirection selon le rôle
    let redirectTo = '';
    if (user.role === 'Tech') {
      redirectTo = 'TechnicianDashboard';
    } else if (user.role === 'Admin') {
      redirectTo = 'Dashboard';
    } else {
      redirectTo = 'Dashboard'; // Par défaut
    }

    console.log('✅ Connexion réussie - Redirection vers:', redirectTo);

    // Réponse de succès
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.id,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role,
        adresse: user.adresse,
        tel: user.tel
      },
      redirectTo: redirectTo,
      token: `auth_token_${user.id}_${Date.now()}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// 📝 Route d'inscription (optionnelle)
app.post('/api/auth/register', async (req, res) => {
  const { nom, prenom, email, password, adresse, tel, role } = req.body;

  try {
    // Validation
    if (!nom || !prenom || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Nom, prénom, email et mot de passe requis'
      });
    }

    // Vérifier si l'email existe
    const emailExists = testUsers.find(u => u.email === email);

    if (emailExists) {
      return res.status(409).json({
        success: false,
        message: 'Cet email est déjà utilisé'
      });
    }

    // Créer le nouvel utilisateur
    const newUser = {
      id: testUsers.length + 1,
      nom,
      prenom,
      email,
      password,
      adresse: adresse || null,
      tel: tel || null,
      role: role || 'Tech'
    };

    testUsers.push(newUser);

    console.log('✅ Nouvel utilisateur créé:', newUser.nom, newUser.prenom);

    // Déterminer la redirection
    const redirectTo = newUser.role === 'Tech' ? 'TechnicianDashboard' : 'Dashboard';

    res.status(201).json({
      success: true,
      message: 'Inscription réussie',
      user: {
        id: newUser.id,
        nom: newUser.nom,
        prenom: newUser.prenom,
        email: newUser.email,
        role: newUser.role,
        adresse: newUser.adresse,
        tel: newUser.tel
      },
      redirectTo: redirectTo,
      token: `auth_token_${newUser.id}_${Date.now()}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'inscription:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur'
    });
  }
});

// 🧪 Route pour lister les utilisateurs de test
app.get('/api/test-users', (req, res) => {
  res.json({
    success: true,
    message: 'Utilisateurs de test disponibles',
    users: testUsers.map(user => ({
      email: user.email,
      password: user.password,
      role: user.role,
      nom: user.nom,
      prenom: user.prenom
    }))
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur d'authentification démarré sur le port ${PORT}`);
  console.log(`📱 API disponible sur: http://localhost:${PORT}`);
  console.log(`🔐 Route de connexion: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`📝 Route d'inscription: POST http://localhost:${PORT}/api/auth/register`);
  console.log(`🧪 Utilisateurs test: GET http://localhost:${PORT}/api/test-users`);
  console.log('');
  console.log('🎯 Comptes de test disponibles:');
  console.log('   Tech: <EMAIL> / Tech123 → TechnicianDashboard');
  console.log('   Admin: <EMAIL> / Admin123 → Dashboard');
  console.log('');
  console.log('✅ Serveur prêt à recevoir des connexions !');
});
