import React, { useState, useEffect } from 'react';
import './src/App.css';

// Composant simple pour la version web
const App = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);

  const API_BASE_URL = 'http://localhost:4000';

  const handleLogin = async (e) => {
    e.preventDefault();
    
    if (!email || !password) {
      alert('Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        setUser(data.user);
        setIsLoggedIn(true);
        alert(`Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}`);
      } else {
        alert('Erreur de connexion: ' + (data.message || 'Erreur de connexion'));
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      alert('Impossible de se connecter au serveur. Vérifiez votre connexion internet et que le serveur est démarré.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUser(null);
    setEmail('');
    setPassword('');
  };

  if (isLoggedIn && user) {
    return (
      <div className="dashboard">
        <header className="dashboard-header">
          <h1>🌊 AquaTrack - Tableau de Bord</h1>
          <div className="user-info">
            <span>Bonjour {user.prenom} {user.nom} ({user.role})</span>
            <button onClick={handleLogout} className="logout-btn">
              Déconnexion
            </button>
          </div>
        </header>
        
        <main className="dashboard-content">
          <div className="welcome-card">
            <h2>Bienvenue sur AquaTrack</h2>
            <p>Système de gestion de facturation d'eau</p>
          </div>
          
          <div className="dashboard-grid">
            <div className="dashboard-card">
              <div className="card-icon">👥</div>
              <h3>Clients</h3>
              <p>Gérer les clients</p>
            </div>
            
            <div className="dashboard-card">
              <div className="card-icon">💧</div>
              <h3>Consommation</h3>
              <p>Saisir les relevés</p>
            </div>
            
            <div className="dashboard-card">
              <div className="card-icon">🧾</div>
              <h3>Factures</h3>
              <p>Consulter les factures</p>
            </div>
            
            <div className="dashboard-card">
              <div className="card-icon">📱</div>
              <h3>Scanner QR</h3>
              <p>Scanner les codes QR</p>
            </div>
          </div>
          
          <div className="info-message">
            <h3>📱 Version Mobile Disponible</h3>
            <p>
              Cette application est optimisée pour React Native. 
              Pour une expérience complète, utilisez la version mobile.
            </p>
            <p>
              <strong>Instructions :</strong>
            </p>
            <ul>
              <li>Installez React Native CLI</li>
              <li>Lancez un émulateur Android/iOS</li>
              <li>Exécutez <code>npx react-native run-android</code> ou <code>npx react-native run-ios</code></li>
            </ul>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="logo">
          <div className="logo-icon">🌊</div>
          <h1>AquaTrack</h1>
          <p>Système de Facturation Mobile</p>
        </div>
        
        <form onSubmit={handleLogin} className="login-form">
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Entrez votre email"
              disabled={loading}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Mot de passe</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Entrez votre mot de passe"
              disabled={loading}
              required
            />
          </div>
          
          <button 
            type="submit" 
            className="login-btn"
            disabled={loading}
          >
            {loading ? 'Connexion...' : 'Se connecter'}
          </button>
        </form>
        
        <div className="test-info">
          <h4>Compte de test :</h4>
          <p>Email: <EMAIL></p>
          <p>Mot de passe: Tech123</p>
        </div>
      </div>
    </div>
  );
};

export default App;
