import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const GoogleMapsPage = ({ navigation }) => {
  // Données simulées des clients avec coordonnées
  const [clients] = useState([
    {
      id: 1,
      nom: '<PERSON><PERSON> Jean',
      adresse: '123 Rue de la Paix, Tunis',
      secteur: 'Centre Ville',
      latitude: 36.8065,
      longitude: 10.1815,
      status: 'Actif'
    },
    {
      id: 2,
      nom: '<PERSON>',
      adresse: '456 Avenue Habib Bourguiba, Tunis',
      secteur: 'Menzah',
      latitude: 36.8189,
      longitude: 10.1658,
      status: 'Actif'
    },
    {
      id: 3,
      nom: '<PERSON>',
      adress<PERSON>: '789 Rue de Carthage, Tunis',
      secteur: 'Bardo',
      latitude: 36.8110,
      longitude: 10.1370,
      status: 'Inactif'
    },
  ]);

  const [selectedSecteur, setSelectedSecteur] = useState('Tous');
  const secteurs = ['Tous', 'Centre Ville', 'Menzah', 'Bardo'];

  const handleOpenMaps = (client) => {
    Alert.alert(
      'Ouvrir dans Google Maps',
      `Voulez-vous ouvrir la localisation de ${client.nom} dans Google Maps ?`,
      [
        { text: 'Annuler', style: 'cancel' },
        { 
          text: 'Ouvrir', 
          onPress: () => {
            // Ici, on ouvrirait Google Maps avec les coordonnées
            console.log(`Ouverture de Google Maps pour ${client.nom}`);
            Alert.alert('Info', 'Ouverture de Google Maps...');
          }
        }
      ]
    );
  };

  const handleShowAllOnMap = () => {
    Alert.alert(
      'Carte Complète',
      'Affichage de tous les clients sur la carte Google Maps',
      [
        { text: 'OK', onPress: () => console.log('Affichage carte complète') }
      ]
    );
  };

  const filteredClients = selectedSecteur === 'Tous' 
    ? clients 
    : clients.filter(client => client.secteur === selectedSecteur);

  const renderClientCard = (client) => (
    <View key={client.id} style={styles.clientCard}>
      <View style={styles.clientHeader}>
        <View style={styles.clientInfo}>
          <Text style={styles.clientName}>{client.nom}</Text>
          <Text style={styles.clientSecteur}>{client.secteur}</Text>
        </View>
        <View style={[
          styles.statusBadge, 
          { backgroundColor: client.status === 'Actif' ? '#10b981' : '#ef4444' }
        ]}>
          <Text style={styles.statusText}>{client.status}</Text>
        </View>
      </View>
      
      <View style={styles.clientDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="location-outline" size={16} color="#6b7280" />
          <Text style={styles.detailText}>{client.adresse}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Ionicons name="navigate-outline" size={16} color="#6b7280" />
          <Text style={styles.detailText}>
            {client.latitude.toFixed(4)}, {client.longitude.toFixed(4)}
          </Text>
        </View>
      </View>
      
      <View style={styles.clientActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => handleOpenMaps(client)}
        >
          <Ionicons name="map" size={16} color="#ef4444" />
          <Text style={styles.actionButtonText}>Voir sur Maps</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.secondaryButton]}
          onPress={() => navigation.navigate('Consommation', { clientId: client.id })}
        >
          <Ionicons name="water" size={16} color="#10b981" />
          <Text style={[styles.actionButtonText, { color: '#10b981' }]}>Relevé</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ef4444" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Localisation Clients</Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.openDrawer()}
        >
          <Ionicons name="menu" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Map Actions */}
        <View style={styles.mapActionsContainer}>
          <TouchableOpacity 
            style={styles.mapActionButton}
            onPress={handleShowAllOnMap}
          >
            <Ionicons name="map-outline" size={24} color="white" />
            <Text style={styles.mapActionText}>Voir Tous sur Carte</Text>
          </TouchableOpacity>
        </View>

        {/* Secteur Filter */}
        <View style={styles.filterContainer}>
          <Text style={styles.filterTitle}>Filtrer par secteur :</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterButtons}>
              {secteurs.map((secteur) => (
                <TouchableOpacity
                  key={secteur}
                  style={[
                    styles.filterButton,
                    selectedSecteur === secteur && styles.filterButtonActive
                  ]}
                  onPress={() => setSelectedSecteur(secteur)}
                >
                  <Text style={[
                    styles.filterButtonText,
                    selectedSecteur === secteur && styles.filterButtonTextActive
                  ]}>
                    {secteur}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{filteredClients.length}</Text>
            <Text style={styles.statLabel}>Clients</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#10b981' }]}>
              {filteredClients.filter(c => c.status === 'Actif').length}
            </Text>
            <Text style={styles.statLabel}>Actifs</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#ef4444' }]}>
              {filteredClients.filter(c => c.status === 'Inactif').length}
            </Text>
            <Text style={styles.statLabel}>Inactifs</Text>
          </View>
        </View>

        {/* Clients List */}
        <ScrollView style={styles.clientsList} showsVerticalScrollIndicator={false}>
          <Text style={styles.listTitle}>
            Clients - {selectedSecteur} ({filteredClients.length})
          </Text>
          {filteredClients.map(renderClientCard)}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#ef4444',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  menuButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  mapActionsContainer: {
    marginBottom: 20,
  },
  mapActionButton: {
    backgroundColor: '#ef4444',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  mapActionText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  filterContainer: {
    marginBottom: 20,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  filterButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  filterButtonActive: {
    backgroundColor: '#ef4444',
    borderColor: '#ef4444',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    flex: 1,
    marginHorizontal: 4,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ef4444',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  clientsList: {
    flex: 1,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  clientCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 2,
  },
  clientSecteur: {
    fontSize: 14,
    color: '#6b7280',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  clientDetails: {
    marginBottom: 12,
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
    flex: 1,
  },
  clientActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 0.48,
    justifyContent: 'center',
  },
  secondaryButton: {
    backgroundColor: '#f0fdf4',
  },
  actionButtonText: {
    fontSize: 14,
    color: '#ef4444',
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default GoogleMapsPage;
