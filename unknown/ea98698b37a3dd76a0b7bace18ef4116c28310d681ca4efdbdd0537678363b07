import React from 'react';

const DashboardAdmin = ({ user, onLogout }) => {
  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>👨‍💼 AquaTrack - Dashboard Administrateur</h1>
        <div className="user-info">
          <span>Bonjour {user ? `${user.prenom} ${user.nom}` : 'Administrateur'} (Admin)</span>
          <button onClick={onLogout || (() => window.location.reload())} className="logout-btn">
            Déconnexion
          </button>
        </div>
      </header>
      
      <main className="dashboard-content">
        <div className="welcome-card">
          <h2>Bienvenue Administrateur</h2>
          <p>Panneau d'administration du système AquaTrack</p>
        </div>
        
        <div className="dashboard-grid">
          <div className="dashboard-card">
            <div className="card-icon">👥</div>
            <h3>Gestion Utilisateurs</h3>
            <p>Gérer les techniciens et administrateurs</p>
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">🏢</div>
            <h3>Gestion Secteurs</h3>
            <p>Configurer les secteurs géographiques</p>
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">📊</div>
            <h3>Rapports</h3>
            <p>Consulter les statistiques et rapports</p>
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">⚙️</div>
            <h3>Configuration</h3>
            <p>Paramètres système et tarification</p>
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">👥</div>
            <h3>Clients</h3>
            <p>Vue d'ensemble des clients</p>
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">💧</div>
            <h3>Consommations</h3>
            <p>Supervision des relevés</p>
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">🧾</div>
            <h3>Factures</h3>
            <p>Gestion globale des factures</p>
          </div>
          
          <div className="dashboard-card">
            <div className="card-icon">💰</div>
            <h3>Finances</h3>
            <p>Suivi des paiements et revenus</p>
          </div>
        </div>
        
        <div className="stats-container">
          <h3>Statistiques Globales</h3>
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-number">0</div>
              <div className="stat-label">Techniciens Actifs</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">0</div>
              <div className="stat-label">Clients Total</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">0</div>
              <div className="stat-label">Factures ce mois</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">0€</div>
              <div className="stat-label">Revenus ce mois</div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DashboardAdmin;
