import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  FlatList,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ConsommationHistoryPage = ({ navigation }) => {
  // Données simulées des consommations
  const [consommations] = useState([
    {
      id: 1,
      client: '<PERSON><PERSON>',
      periode: '2024-01',
      consommationPre: 150,
      consommationActuelle: 175,
      difference: 25,
      date: '2024-01-15',
      status: 'Validé'
    },
    {
      id: 2,
      client: '<PERSON>',
      periode: '2024-01',
      consommationPre: 200,
      consommationActuelle: 230,
      difference: 30,
      date: '2024-01-16',
      status: 'En attente'
    },
    {
      id: 3,
      client: '<PERSON>',
      periode: '2024-01',
      consommationPre: 180,
      consommationActuelle: 195,
      difference: 15,
      date: '2024-01-17',
      status: 'Validé'
    },
  ]);

  const [searchText, setSearchText] = useState('');

  const getStatusColor = (status) => {
    return status === 'Validé' ? '#10b981' : '#f59e0b';
  };

  const getStatusIcon = (status) => {
    return status === 'Validé' ? 'checkmark-circle' : 'time';
  };

  const renderConsommationItem = ({ item }) => (
    <View style={styles.consommationCard}>
      <View style={styles.cardHeader}>
        <Text style={styles.clientName}>{item.client}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
          <Ionicons 
            name={getStatusIcon(item.status)} 
            size={14} 
            color={getStatusColor(item.status)} 
          />
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status}
          </Text>
        </View>
      </View>
      
      <View style={styles.cardContent}>
        <View style={styles.detailRow}>
          <Ionicons name="calendar-outline" size={16} color="#6b7280" />
          <Text style={styles.detailText}>Période: {item.periode}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Ionicons name="calendar-outline" size={16} color="#6b7280" />
          <Text style={styles.detailText}>Date relevé: {item.date}</Text>
        </View>
        
        <View style={styles.consumptionDetails}>
          <View style={styles.consumptionItem}>
            <Text style={styles.consumptionLabel}>Précédente</Text>
            <Text style={styles.consumptionValue}>{item.consommationPre} m³</Text>
          </View>
          
          <View style={styles.consumptionItem}>
            <Text style={styles.consumptionLabel}>Actuelle</Text>
            <Text style={styles.consumptionValue}>{item.consommationActuelle} m³</Text>
          </View>
          
          <View style={styles.consumptionItem}>
            <Text style={styles.consumptionLabel}>Consommée</Text>
            <Text style={[styles.consumptionValue, styles.consumptionDifference]}>
              {item.difference} m³
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#06b6d4" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Historique Consommations</Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => navigation.openDrawer()}
        >
          <Ionicons name="menu" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#6b7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Rechercher un client..."
            value={searchText}
            onChangeText={setSearchText}
            placeholderTextColor="#9ca3af"
          />
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{consommations.length}</Text>
            <Text style={styles.statLabel}>Total Relevés</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#10b981' }]}>
              {consommations.filter(c => c.status === 'Validé').length}
            </Text>
            <Text style={styles.statLabel}>Validés</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statNumber, { color: '#f59e0b' }]}>
              {consommations.filter(c => c.status === 'En attente').length}
            </Text>
            <Text style={styles.statLabel}>En attente</Text>
          </View>
        </View>

        {/* Liste des consommations */}
        <View style={styles.listContainer}>
          <Text style={styles.listTitle}>Mes Relevés</Text>
          
          <FlatList
            data={consommations}
            renderItem={renderConsommationItem}
            keyExtractor={(item) => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#06b6d4',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  menuButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: '#1f2937',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    flex: 1,
    marginHorizontal: 4,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#06b6d4',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  listContainer: {
    flex: 1,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  listContent: {
    paddingBottom: 20,
  },
  consommationCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  cardContent: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  consumptionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  consumptionItem: {
    alignItems: 'center',
  },
  consumptionLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  consumptionValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  consumptionDifference: {
    color: '#06b6d4',
  },
});

export default ConsommationHistoryPage;
